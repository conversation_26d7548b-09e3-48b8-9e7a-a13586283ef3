plugins {
    id 'com.android.library'
    id 'kotlin-android-extensions'
    id 'kotlin-android'
}

android {
    namespace 'com.iandroid.allclass.lib_common'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        profile {
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(':lib_basecore')
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.0"
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.squareup.moshi:moshi:1.8.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-runtime:2.2.0'
    api 'io.reactivex.rxjava2:rxjava:2.2.7'
    api 'io.reactivex.rxjava2:rxandroid:2.1.1'
    api 'com.alibaba:fastjson:1.2.73'
    api 'com.alibaba:fastjson:1.1.72.android'
    implementation 'com.google.code.gson:gson:2.8.5'
    api 'com.squareup.retrofit2:retrofit:2.6.0'
    api('com.squareup.retrofit2:converter-gson:2.6.0') {
        exclude group: 'com.squareup.okhttp3'
    }
    implementation 'com.squareup.okhttp3:okhttp:4.8.0'
    implementation("com.jakewharton.retrofit:retrofit2-rxjava2-adapter:1.0.0") {
        exclude module: 'retrofit'
    }
    implementation 'com.google.android.material:material:1.5.0'

    implementation 'com.github.chrisbanes:PhotoView:2.2.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    api('com.github.bumptech.glide:glide:4.13.2')
    api('com.github.bumptech.glide:okhttp3-integration:4.13.2')
    annotationProcessor("com.github.bumptech.glide:compiler:4.13.2")
    api('jp.wasabeef:glide-transformations:4.3.0')
    // litePal数据库
    api 'org.litepal.guolindev:core:3.2.3'
    //图表库
    api 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    // exoplayer
    api("com.google.android.exoplayer:exoplayer-core:2.18.7") {
        exclude group: 'androidx.core'
    }
    api 'com.google.android.exoplayer:exoplayer-ui:2.18.7'
    api 'io.github.csdn-mobile:RoundView:1.8.0'
    api 'com.github.chrisbanes:PhotoView:2.3.0'
}