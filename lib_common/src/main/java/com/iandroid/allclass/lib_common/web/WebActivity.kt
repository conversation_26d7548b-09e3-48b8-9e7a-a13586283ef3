package com.iandroid.allclass.lib_common.web

import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.WebChromeClient
import android.widget.FrameLayout
import com.iandroid.allclass.lib_basecore.base.BaseActivity
import com.iandroid.allclass.lib_common.R
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.AlbumActionEntity
import com.iandroid.allclass.lib_common.beans.ImmersiveEntity
import com.iandroid.allclass.lib_common.beans.RightTitleEntity
import com.iandroid.allclass.lib_common.beans.UpLoadImgBean
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.event.CourseCategoryPassEvent
import com.iandroid.allclass.lib_common.event.CourseChangeEvent
import com.iandroid.allclass.lib_common.event.EventImgUpLoad
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.AlbumUtils
import com.iandroid.allclass.lib_common.utils.PermissionUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getQueryParamsByKey
import com.iandroid.allclass.lib_common.utils.exts.objFromIntentParam
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toIntSafely
import com.iandroid.allclass.lib_common.web.config.WebViewConfig
import com.iandroid.allclass.lib_common.web.js.JsSpecialUiImpl
import com.iandroid.allclass.lib_common.web.js.invokeToH5_JSCalendarAccessCallback
import com.iandroid.allclass.lib_common.web.js.invokeToH5_PhotoAlbumCallBack
import com.iandroid.allclass.lib_common.web.view.AndroidBug5497Workaround
import com.iandroid.allclass.lib_common.web.view.LangWebView
import io.reactivex.disposables.CompositeDisposable
import kotlinx.android.synthetic.main.content_web.bottomBtn
import kotlinx.android.synthetic.main.content_web.bottomView
import kotlinx.android.synthetic.main.content_web.id_com_status
import kotlinx.android.synthetic.main.content_web.id_content_webview

/**
created by wangkm
on 2020/9/22.
 */
open class WebActivity : BaseActivity() {
    var webIntent: WebIntent? = null
    private var countDownTimer: CountDownTimer? = null
    var curAlbumActionEntity: AlbumActionEntity = AlbumActionEntity()
    var compositeDisposable: CompositeDisposable = CompositeDisposable()

    /**
     * 视频全屏参数
     */
    private val COVER_SCREEN_PARAMS = FrameLayout.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT
    )
    private var customView: View? = null
    private var fullscreenContainer: FrameLayout? = null
    private var customViewCallback: WebChromeClient.CustomViewCallback? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.content_web)
        AndroidBug5497Workaround.assistActivity(this)
        initPageFrom()
    }

    override fun immersiveIntoStatusBar(): Boolean {
        webIntent?.run {
            if (this.jsTitleBarValue < 0) {
                //jsTitleBarValue小于零，是否沉浸式，从url里面读取
                url?.also {
                    var titleBar = it.getQueryParamsByKey("show_title")
                    if (titleBar.isNullOrEmpty()) titleBar = "1"
                    this.showTitle = titleBar.toIntSafely() == 1
                }
            } else { //否则，根据js的值来处理沉浸式
                showTitle = (jsTitleBarValue > 0)
            }

            showTitleBar(showTitle)
            return !showTitle
        }
        return super.immersiveIntoStatusBar()
    }

    override fun initBaseContent() {
        super.initBaseContent()
        compositeDisposable?.add(SimpleRxBus.observe(EventImgUpLoad::class) {
            id_content_webview.invokeToH5_PhotoAlbumCallBack(it.url.orEmpty(), 0)
        })

        id_content_webview.initWebViewSetting(
            this@WebActivity, object : JsSpecialUiImpl(id_com_status, id_content_webview) {
                override fun onReceivedTitle(title: String?) {
                    <EMAIL>(title)
                }

                override fun JsGetReportData(): String {
                    return webIntent?.page_param.orEmpty()
                }

                override fun DoJSStatusBar(immersiveEntity: ImmersiveEntity) {
                    super.DoJSStatusBar(immersiveEntity)
                    webIntent?.run {
                        <EMAIL>?.jsTitleBarValue = immersiveEntity.show_title
                        <EMAIL>()
                    }
                }

                override fun DoJSCloseWindow(viewID: String?) {
                    super.DoJSCloseWindow(viewID)
                    if (!isFinishing) finish()
                }

                override fun DoJSUpdateTitlebarRightAction(rightTitleEntity: RightTitleEntity?) {
                    super.DoJSUpdateTitlebarRightAction(rightTitleEntity)
                    rightTitleEntity?.run {

                    }
                }

                override fun DoJSPhotoAlbum(albumActionEntity: AlbumActionEntity) {
                    super.DoJSPhotoAlbum(albumActionEntity)
                    curAlbumActionEntity = albumActionEntity
                    AlbumUtils.showPhotoChooseSheetDialog(
                        this@WebActivity,
                        UpLoadImgBean(
                            UpLoadImgBean.UPLOAD_FACE,
                            "",
                            curAlbumActionEntity.type
                        ).also {
                            it.hintCamera = curAlbumActionEntity.camera == 0
                        }
                    ) { cancel ->
                        if (cancel) {
                            id_content_webview.invokeToH5_PhotoAlbumCallBack("", 0, 1)
                        }
                    }
                }
            }, WebViewConfig()
                .setProceedSslError(false)
                .setNeedHandleJsAlert(true)
                .setOpenHardWareAcc(true)
        )

        webIntent = intent.objFromIntentParam<WebIntent>(Values.intentJsonParam)
        webIntent?.run {
            setWebVideoScreen()
            id_content_webview.startLoadUrl(url)
        }
    }

    /**
     * 支持H5视频全屏
     */
    private fun setWebVideoScreen() {
        id_content_webview.setOnVideoScreenCallback(object : LangWebView.OnVideoScreenCallback {

            override fun onShowCustomView(
                view: View?,
                callback: WebChromeClient.CustomViewCallback?
            ) {
                showCustomView(view, callback)
            }

            override fun onHideCustomView() {
                hideCustomView()
            }
        })
    }

    private fun showCustomView(view: View?, callback: WebChromeClient.CustomViewCallback?) {
        if (customView != null) {
            callback!!.onCustomViewHidden()
            return
        }

        this.window.decorView
        val decor = window.decorView as FrameLayout
        fullscreenContainer = LangWebView.FullscreenHolder(this)
        fullscreenContainer!!.addView(view, COVER_SCREEN_PARAMS)
        decor.addView(fullscreenContainer, COVER_SCREEN_PARAMS)
        customView = view
        setStatusBarVisibility(false)
        customViewCallback = callback
    }

    private fun hideCustomView() {
        if (customView == null) return

        setStatusBarVisibility(true)
        val decor = window.decorView as FrameLayout
        decor.removeView(fullscreenContainer)
        fullscreenContainer = null
        customView = null
        customViewCallback!!.onCustomViewHidden()
        id_content_webview.visibility = View.VISIBLE

    }

    private fun setStatusBarVisibility(visible: Boolean) {
        val flag = if (visible) 0 else WindowManager.LayoutParams.FLAG_FULLSCREEN
        window.setFlags(flag, WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }

    private fun initPageFrom() {
        webIntent?.let { intent ->
            if (intent.pageFrom == ActionType.actionCourseList) {
                // 课程状态发生了改变，更新该课程
                compositeDisposable?.add(SimpleRxBus.observe(CourseChangeEvent::class) {
                    if (it.course != null)
                        intent.actionParam = it.course!!
                })
                compositeDisposable?.add(SimpleRxBus.observe(CourseCategoryPassEvent::class) {
                    // 课程答题成功了，通知关闭课程详情页
                    this.finish()
                })
                // 来自课程列表时，显示Quiz按钮
                bottomView.show(true)
                bottomBtn.text = buildString {
                    append("Complete the Quiz (60s)")
                }
                startCountdown(60)
                bottomBtn.clickWithTrigger {
                    routeAction(ActionType.actionQuestionnaire) {
                        it.param = intent.actionParam
                    }
                }
            }
        }
    }

    private fun startCountdown(seconds: Int) {
        // 设置为不可点击
        bottomBtn.isEnabled = false
        bottomBtn.setBackgroundResource(R.color.color_d9d9d9)
        countDownTimer?.cancel()
        // 使用 CountDownTimer 实现倒计时
        countDownTimer = object : CountDownTimer(seconds * 1000L, 1000L) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsRemaining = millisUntilFinished / 1000
                bottomBtn.text = buildString {
                    append("Complete the Quiz ")
                    append("(${secondsRemaining}s)")
                }
            }

            override fun onFinish() {
                // 恢复为可点击状态
                bottomBtn.setBackgroundResource(R.color.cl_262626)
                bottomBtn.text = buildString {
                    append("Complete the Quiz ")
                }
                bottomBtn.isEnabled = true
            }
        }.start()
    }

    override fun onResume() {
        super.onResume()
        webIntent?.let { intent ->
            if (intent.pageFrom == ActionType.actionCourseList)
                startCountdown(30)
        }
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_BACK -> {
                // 回退键 事件处理 优先级:视频播放全屏-网页回退-关闭页面
                if (customView != null) {
                    hideCustomView()
                } else if (id_content_webview.canGoBack()) {
                    id_content_webview.goBack()
                } else {
                    finish()
                }
                true
            }

            else -> super.onKeyUp(keyCode, event)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Values.waitcode_choosepic_from_camera
            || requestCode == Values.waitcode_choosepic_from_album
            || requestCode == Values.waitcode_crop_image
        ) {
            if (RESULT_CANCELED == resultCode) {
                id_content_webview.invokeToH5_PhotoAlbumCallBack("", 0, 1)
            } else if (curAlbumActionEntity.crop > 0 && requestCode != Values.waitcode_crop_image)
                AlbumUtils.onActivityResult(this, requestCode, resultCode, data)
            else if (resultCode == RESULT_OK) {
                AlbumUtils.albumSelectResultToH5(
                    id_content_webview,
                    requestCode,
                    data,
                    curAlbumActionEntity?.upload == 1
                )
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionUtils.REQUEST_CALENDAR_CODE) {
            val canAccess = (PermissionUtils.checkSelfPermissionGroup(
                this,
                PermissionUtils.PERMISSIONS_CALENDAR_GROUP
            ) == PackageManager.PERMISSION_GRANTED)
            id_content_webview.invokeToH5_JSCalendarAccessCallback(if (canAccess) 1 else 0)
        } else if (requestCode == PermissionUtils.REQUEST_CAMERA_CODE
            || requestCode == PermissionUtils.REQUEST_ALBUM_CODE
        ) {
            if (!AlbumUtils.onRequestPermissionsResult(this, requestCode, grantResults)) {
                id_content_webview.invokeToH5_PhotoAlbumCallBack("", 0, 1)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (webIntent?.pageFrom == ActionType.actionPayrollAccount) {
            SimpleRxBus.post(UINewAnchorTaskStatusEvent())
        }
        countDownTimer?.cancel()
        id_content_webview?.clear()
        compositeDisposable.clear()
        compositeDisposable.dispose()
    }
}
