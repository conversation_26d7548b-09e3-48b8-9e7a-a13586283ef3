package com.iandroid.allclass.lib_common

import android.content.Context
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.iandroid.allclass.lib_common.utils.DirectoryUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import java.io.File
import java.io.IOException


/**
 * <AUTHOR>
 * @date 2021/3/23.
 */
class VoiceRecordPlayer(context: Context? = null) {

    private val TAG: String = VoiceRecordPlayer::class.java.name

    private var mMediaRecorderCallBack: MediaRecorderCallBack? = null

    private var mMediaRecorder: MediaRecorder? = null
    private var isRecording = false

    //已完成录音路径
    private var path: String? = null

    private var mContext: Context? = null

    //输出文件
    private var mFile: File? = null

    //最大录制秒
    private var mMaximum = 60 * 1000

    //当前录制了多少秒
    private var mSecond = 0

    //当前播放了多少秒
    private var mPlaySecond = 0

    //设置录音最大时间，0为不设置
    private var mHandler: Handler = Handler(Looper.getMainLooper())

    init {
        this.mContext = context
    }


    private var mRunnable: Runnable = object : Runnable {
        override fun run() {

            //最大录制时间不等于0时就是已经设置了
            if (mSecond * 1000 > mMaximum) {
                mHandler.removeCallbacks(this)
                stopRecordVoice()
                mSecond = 0
            } else {
                mHandler.postDelayed(this, 1000)
                mMediaRecorderCallBack?.recordProcess(true, ++mSecond)
            }
        }
    }

    private var playerRunnable: Runnable = object : Runnable {
        override fun run() {
            mHandler.postDelayed(this, 1000)
            mMediaRecorderCallBack?.recordProcess(false, ++mPlaySecond)
        }
    }


    /**
     * 更新录音时的db
     */
    var BASE: Int = 1
    private var dbRunnable: Runnable = object : Runnable {
        override fun run() {
            mMediaRecorder?.let {
                var ratio: Double = (it.maxAmplitude / BASE).toDouble()
                var db = 0// 分贝
                if (ratio > 1) db = (20 * Math.log10(ratio)).toInt()
                mMediaRecorderCallBack?.updateDb(db)
                mHandler.postDelayed(this, 200)
            }
        }
    }

    /**
     * 最大秒数限制 0为默认不设置
     * @param second 秒
     */
    fun setMaximum(second: Int) {
        mMaximum = second * 1000
    }

    private fun initMediaRecorder() {

        if (mMediaRecorder == null) {
            mMediaRecorder = MediaRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.DEFAULT)
                setAudioEncoder(MediaRecorder.AudioEncoder.HE_AAC)
                setAudioSamplingRate(8000)
            }
            mContext?.takeIf { mFile == null }?.let {
                mFile = File(DirectoryUtils.getFilesDirectory(it, "recorder"), "centerVoice.wav")
            }

            mFile?.let {
                it.takeIf { !it.exists() }?.run { createNewFile() }
                path = it.absolutePath
                mMediaRecorder?.setOutputFile(path)
            }
        }
    }

    //是否正在录音
    fun isRecording(): Boolean {
        return isRecording
    }

    //获取录音文件路径
    fun getPath(): String? {
        return path
    }

    //获取当前录制的时间
    fun getCurrentRecordTime(): Int {
        return mSecond
    }

    //开始录音
    fun startRecordVoice() {
        initMediaRecorder()
        clearPlayer()
        mMediaRecorder?.takeIf { !isRecording }?.run {
            mSecond = 0
            isRecording = true
            try {
                prepare()
            } catch (e: IOException) {
                Log.e(TAG, "prepare() failed")
            }
            start() //开始录制
            mMediaRecorderCallBack?.startRecord()
            if (mMaximum > 0) mHandler.postDelayed(mRunnable, 1000)
            mHandler.post(dbRunnable)
        }
    }

    fun stopRecordVoice() {
        mMediaRecorder?.takeIf { isRecording }?.run {
            mHandler.removeCallbacks(mRunnable)
            mHandler.removeCallbacks(dbRunnable)
            mMediaRecorderCallBack?.stopRecord(path.orEmpty(), mSecond)
            isRecording = false
            stop()
            release()
            mMediaRecorder = null
        }
    }

    fun onDestroy() {

        mMediaRecorder?.takeIf { isRecording }?.run {
            isRecording = false
            stop()
            release()
        }
        mMediaRecorder = null
        mHandler.removeCallbacks(mRunnable)
        mHandler.removeCallbacks(dbRunnable)
        clearPlayer()
    }

    fun setMediaRecorderCallBack(mediaRecorderCallBack: MediaRecorderCallBack) {
        mMediaRecorderCallBack = mediaRecorderCallBack
    }


    interface MediaRecorderCallBack {
        fun startRecord() //开始录制
        fun stopRecord(filePath: String, time: Int) //停止录制
        fun updateDb(db:Int)
        fun playComplete()
        fun recordProcess(isRecord: Boolean, second: Int) //录制中
        fun startPlay() //开始播放
    }


    // 开始播放
    var mediaPlayer: MediaPlayer? = null
    var loadingRes: Boolean = false
    fun startPlayVoice(path: String) {

        if (loadingRes) {
            ToastUtils.showToast(R.string.res_loading)
            return
        }

        clearPlayer()
        if (mediaPlayer == null) mediaPlayer = MediaPlayer()
        mediaPlayer?.takeIf { path.isNotEmpty() }?.run {
            try {
                loadingRes = true
                setDataSource(path)
                prepareAsync()
                setOnCompletionListener {
                    loadingRes = false
                    mHandler.removeCallbacks(playerRunnable)
                    mMediaRecorderCallBack?.playComplete()
                }
                setOnPreparedListener {
                    loadingRes = false
                    start()
                    mMediaRecorderCallBack?.startPlay()
                    mHandler.postDelayed(playerRunnable, 1000)
                }
            } catch (e: IOException) {
                clearPlayer()
            }
        }
    }

    fun pausePlayVoice() {
        mediaPlayer?.run {
            mMediaRecorderCallBack?.playComplete()
            clearPlayer()
        }
    }

    private fun clearPlayer() {
        loadingRes = false
        mPlaySecond = 0
        mHandler.removeCallbacks(playerRunnable)
        mediaPlayer?.run {
            stop()
            reset()
            release()
            mediaPlayer = null
        }
    }

    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying ?: false
    }
}