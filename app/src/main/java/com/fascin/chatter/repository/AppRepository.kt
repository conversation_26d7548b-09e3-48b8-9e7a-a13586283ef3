package com.fascin.chatter.repository

import com.fascin.chatter.R
import com.fascin.chatter.bean.ActivitiesEntity
import com.fascin.chatter.bean.AiAutoChatEntity
import com.fascin.chatter.bean.AnchorShiftEntity
import com.fascin.chatter.bean.AppGuideEntity
import com.fascin.chatter.bean.BonusMatchHubEntity
import com.fascin.chatter.bean.BonusTipEntity
import com.fascin.chatter.bean.ChatFlagEntity
import com.fascin.chatter.bean.ChatNoticeEntity
import com.fascin.chatter.bean.ChatterMatchedEntity
import com.fascin.chatter.bean.CheckCodeEntity
import com.fascin.chatter.bean.CheckPosEntity
import com.fascin.chatter.bean.CheckRepossessionEntity
import com.fascin.chatter.bean.CourseClassEntity
import com.fascin.chatter.bean.CourseSortEntity
import com.fascin.chatter.bean.DailyRankEntity
import com.fascin.chatter.bean.ExaminationEntity
import com.fascin.chatter.bean.FcCountChangeEntity
import com.fascin.chatter.bean.GoalsEntity
import com.fascin.chatter.bean.IMPenaltiesData
import com.fascin.chatter.bean.ImproveEntity
import com.fascin.chatter.bean.InviteFriendEntity
import com.fascin.chatter.bean.InviteHistoryListEntity
import com.fascin.chatter.bean.IsBlockEntity
import com.fascin.chatter.bean.LeaveRecordsEntity
import com.fascin.chatter.bean.LeaveRuleEntity
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.MineInfoEntity
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.MpcEntity
import com.fascin.chatter.bean.MsgEmojiEntity
import com.fascin.chatter.bean.MsgGreetTabEntity
import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.bean.NewbieCenterEntity
import com.fascin.chatter.bean.NotMatchModelEntity
import com.fascin.chatter.bean.NoticeEntity
import com.fascin.chatter.bean.OnlineTagEntity
import com.fascin.chatter.bean.PenaltyHistoryEntity
import com.fascin.chatter.bean.PrivacyTabEntity
import com.fascin.chatter.bean.RegActivateEntity
import com.fascin.chatter.bean.RegisterEnableEntity
import com.fascin.chatter.bean.RevitalizeEntity
import com.fascin.chatter.bean.RevitalizeInEntity
import com.fascin.chatter.bean.SchedulePerContentEntity
import com.fascin.chatter.bean.SchedulePerRulesEntity
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.bean.SelectModelEntity
import com.fascin.chatter.bean.ShiftChangeListEntity
import com.fascin.chatter.bean.ShiftChangeRuleEntity
import com.fascin.chatter.bean.ShiftEntranceEntity
import com.fascin.chatter.bean.SubmitAnswerEntity
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.bean.TaskEntity
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.bean.TodayLeaveEntity
import com.fascin.chatter.bean.WarningCheckEntity
import com.fascin.chatter.config.Config
import com.fascin.chatter.utils.StatusHeartbeatManager
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.UserController.checkValid
import com.iandroid.allclass.lib_common.beans.AppUpdateEntity
import com.iandroid.allclass.lib_common.beans.AuthUserEntity
import com.iandroid.allclass.lib_common.beans.ConversationEntity
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.beans.GreetListEntity
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.PassWithdrawReq
import com.iandroid.allclass.lib_common.beans.ProfileTagEntity
import com.iandroid.allclass.lib_common.beans.ProfitListEntity
import com.iandroid.allclass.lib_common.beans.PropertyInfoEntity
import com.iandroid.allclass.lib_common.beans.UIEventAppUpdate
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.beans.WithdrawListEntity
import com.iandroid.allclass.lib_common.beans.WithdrawNumEntity
import com.iandroid.allclass.lib_common.beans.WithdrawNumReq
import com.iandroid.allclass.lib_common.beans.WithdrawProfitReq
import com.iandroid.allclass.lib_common.beans.WithdrawReq
import com.iandroid.allclass.lib_common.beans.base.checkData
import com.iandroid.allclass.lib_common.beans.base.checkDataWithRetmsg
import com.iandroid.allclass.lib_common.event.EventParamsBuilder
import com.iandroid.allclass.lib_common.event.InAppOnlineSwitchEvent
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.network.ServiceFactory
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
created by wangkm
on 2020/9/15.
 */
object AppRepository {
    private val api = ServiceFactory.get(AppService::class.java)

    /**
     * 获取版本到期等弹框信息
     */
    fun fetchAppUpgradeInfo(fromSettingMenu: Boolean = false) {
        api.fetchAppUpgradeInfo().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({ httpRes ->
                httpRes?.data?.run {
                    this.toJsonString().jsonToObj<AppUpdateEntity>()?.also {
                        AppController.appUpdateEntity = it
                        SimpleRxBus.post(UIEventAppUpdate(it))
                        AppContext.context.routeAction(ActionType.actionUpdateDialog) { action ->
                            it.forceShowDialog = fromSettingMenu
                            action.param = it
                        }
                        if (fromSettingMenu && it.type == AppUpdateEntity.NO_UPDATE) {
                            ToastUtils.showToast(R.string.already_latest_ver)
                        }
                    }
                }
            }, {

            })
    }

    /**
     *登录
     */
    fun accountLogin(account: String, password: String): Single<AuthUserEntity> {
        return api.accountLogin(account, password).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map {
                if (it.isSuccessful()) {
                    it.data?.run {
                        if (this.regStatus == 0) {
                            UserController.login(this)
                        }
                        StatusHeartbeatManager.stopHeartbeat()
                        StatusHeartbeatManager.startHeartbeat()
                    }
                    if (!it.data.checkValid()) {
                        it.code = -101
                    }
                }
                it.checkData()
            }
    }

    fun simpleUsersInfo(userIds: String): Single<List<UserEntity>> {
        return api.simpleUsersInfo(userIds).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getMineInfo(): Single<MineInfoEntity> {
        return api.getMineInfo().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getUserInfo(userId: String, from: Int): Single<UserEntity> {
        return api.getUserInfo(userId, from).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun notifyChange(config: Int, noticeType: Int = 0) {
        api.notifyChange(config).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({
                SPUtils.put(AppContext.context, SPConstants.KEY_PUSH_CONFIG, config)
                if (noticeType == 1) {
                    SimpleRxBus.post(InAppOnlineSwitchEvent())
                }
            }, {
                ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
            })
    }

    fun getTagList(): Single<List<ProfileTagEntity>> {
        return api.getTagList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getPrivacyTagList(): Single<List<PrivacyTabEntity>> {
        return api.getPrivacyTagList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun collectMedia(mediaId: String, collect: Boolean): Single<Any> {
        return api.collectMedia(mediaId, collect).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }


    fun getAlbumMediaList(
        imId: String, lastId: String, type: Int, from: Int, mediaType: Int, tag: Int
    ): Single<ArrayList<MediaEntity>> {
        return api.getAlbumMediaList(imId, lastId, type, from, mediaType, tag)
            .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getChatterMatchedList(
        online: Int,
        member: Int,
        lastId: Int,
        noIce: Int
    ): Single<ChatterMatchedEntity> {
        return api.getChatterMatchedList(online, member, lastId, noIce).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getChatterModelList(): Single<ArrayList<ModelUserEntity>> {
        return api.getChatterModelList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun sayHi(userId: String): Single<String> {
        return api.sayHi(userId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkDataWithRetmsg()
            }
    }

    fun readNewMatch(): Single<String> {
        return api.readNewMatch().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkDataWithRetmsg()
            }
    }

    fun readNewSchedule(): Single<String> {
        return api.readNewSchedule().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkDataWithRetmsg()
            }
    }

    fun addMessageOrGreeting(userId: String, type: Int, content: String): Single<MsgContentEntity> {
        return api.addMessageOrGreeting(userId, type, content).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun modifyMessageOrGreeting(msgID: Int, content: String): Single<MsgContentEntity> {
        return api.modifyMessageOrGreeting(msgID, content).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun delMessageOrGreeting(userId: String, msgID: Int): Single<MessageSettingEntity> {
        return api.delMessageOrGreeting(userId, msgID).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }


    fun openPrivacyMsg(to_msgid: String, from_msgid: String, media_id: Int): Single<String> {
        return api.openPrivacyMsg(to_msgid, from_msgid, media_id).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkDataWithRetmsg()
            }
    }

    fun openPrivacyMultipleMsg(to_msgid: String, from_msgid: String): Single<String> {
        return api.openPrivacyMultipleMsg(to_msgid, from_msgid).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkDataWithRetmsg()
            }
    }

    /**
     * 上报当前客户端状态
     */
    fun statusChange(status: Int) {
        val maxRetries = 20 // 最大重试次数
        // 重置退后台的时间
        if (status == Config.statusforeground) AppController.inBackTime = 0L
        if (!UserController.hasLoggedIn()) return
        fun performStatusChange(retries: Int) {
            api.statusChange(status).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread()).subscribe({
                    if (status == Config.statusBackground) {
                        // 记录app进入后台的时间
                        AppController.inBackTime = System.currentTimeMillis()
                    }
                    it.data?.apply {
                        SimpleRxBus.post(this)
                    }
                }, {
                    if (retries < maxRetries) {
                        performStatusChange(retries + 1)
                    }
                })
        }
        performStatusChange(0)
    }

    //客户端状态心跳检测
    fun statusHeartbeat() {
        api.statusHeartbeat()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({}, {})
    }


    fun getTasksInfo(taskID: Int): Single<TaskEntity> {
        return api.getTasksInfo(taskID).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getTaskCatalogInfo(): Single<List<TaskCatalogEntity>> {
        return api.getTaskCatalogInfo().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getTaskHistoryList(): Single<List<TaskWeekTitleEntity>> {
        return api.getTaskHistoryList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    //活动 Banner
    fun getActivitiesList(): Single<ArrayList<ActivitiesEntity>> {
        return api.getActivitiesList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun sendFlashChat(
        userId: String?, modelId: String?, msg: String?, stamp: String, from: Int?
    ): Single<FcCountChangeEntity> {
        return api.sendFlashChat(userId, modelId, msg, from, stamp).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getNotMatchModel(userId: String?): Single<NotMatchModelEntity> {
        return api.getNotMatchModel(userId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getCoursesSort(): Single<List<CourseSortEntity>> {
        return api.getCoursesSort().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getCourses(lastId: Int, categoryId: Int, size: Int = 10): Single<List<CourseClassEntity>> {
        return api.getCourses(lastId, categoryId, size).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun getQuestions(courseId: Int): Single<ExaminationEntity> {
        return api.getQuestions(courseId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun submitAnswers(id: Int, version: Int, answers: String): Single<SubmitAnswerEntity> {
        return api.submitAnswers(id, version, answers).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun requestGreet(userId: String?): Single<List<MsgGreetTabEntity>> {
        return api.requestGreet(userId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                if (result.data == null) result.data = emptyList()
                result.checkData()
            }
    }

    fun requestEmojis(): Single<List<MsgEmojiEntity>> {
        return api.requestEmojis().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                if (result.data == null) result.data = emptyList()
                result.checkData()
            }
    }

    fun requestSayHiMsg(imId: String): Single<GreetListEntity> {
        return api.requestSayHiMsg(imId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun matchDialogSayHi(
        imId: String, gid: String, content: String
    ): Single<Any> {
        return api.matchDialogSayHi(imId, gid, content).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 更新会话隐身、在线、免打扰开关状态
     */
    fun updateChatFlag(imUid: String, chatFlag: Long): Single<ChatFlagEntity> {
        return api.updateChatFlag(imUid, chatFlag).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 更新联系人聊天tag
     */
    fun updateContactTag(imId: String, tagId: Int): Single<Any> {
        return api.updateContactTag(imId, tagId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取会话记录列表
     */
    fun getFindChatsTabList(modelId: Int): Single<ArrayList<ExploreUserEntity>> {
        return api.getFindChatsTabList(modelId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取VIP合并展示的数据
     */
    fun getVIPConflateList(): Single<ArrayList<ExploreUserEntity>> {
        return api.getVIPConflateList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取置顶model两周内的会话
     */
    fun getFindLostList(modelId: String): Single<ArrayList<ExploreUserEntity>> {
        return api.getFindLostList(modelId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 聊天页Chat Notice
     */
    fun getChatNotice(userId: String): Single<List<ChatNoticeEntity>> {
        return api.chatNotice(userId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 聊天页 关闭指定Chat Notice 通知
     */
    fun chatNoticeClose(userId: String, id: Int): Single<Any> {
        return api.chatNoticeClose(userId, id).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 通知权限开关状态
     */
    fun pushFlagUpdate(pushFlag: Int): Single<Any> {
        return api.pushFlagUpdate(pushFlag).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 数据埋点
     */
    fun eventTrace(key: String, block: (EventParamsBuilder.() -> Unit)? = null) {
        val builder = EventParamsBuilder()
        block?.invoke(builder)
        val params = builder.build()
        api.eventTrace(key, if (params.isEmpty()) "" else params.toJsonString())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({}, {})
    }

    /**
     * 获取低流量配置
     */
    fun matchPolicyGet(): Single<MatchPolicyEntity> {
        return api.matchPolicyGet().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 设置低流量
     */
    fun matchPolicySet(enable: Int): Single<Any> {
        return api.matchPolicySet(enable).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }


    /**
     * 获取improve method弹窗的数据
     */
    fun getImproveMethod(id: String): Single<ImproveEntity> {
        return api.getImproveMethod(id).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 主播绩效中心
     */
    fun chatMpcData(): Single<MpcEntity> {
        return api.chatMpcData().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * MPC更新气泡通知检查
     */
    fun checkHasMpcNotice(): Single<MpcEntity> {
        return api.checkHasMpcNotice().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * MPC更新气泡通知检查
     */
    fun checkHasGoals(): Single<GoalsEntity> {
        return api.checkHasGoals().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取定向建联剩余次数
     */
    fun bonusMatchHubTime(): Single<BonusMatchHubEntity> {
        return api.bonusMatchHubTime().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 定向建联
     */
    fun bonusMatchHubList(): Single<BonusMatchHubEntity> {
        return api.bonusMatchHubList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 定向建联 Like、Flash Chat操作
     */
    fun bonusMatchHubChoose(
        userId: String,
        mid: String,
        chooseType: Int,
        msg: String
    ): Single<BonusMatchHubEntity> {
        return api.bonusMatchHubChoose(userId, mid, chooseType, msg).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }


    /**
     * 检查model是否被用户拉黑
     */
    fun checkIsBlock(imID: String): Single<IsBlockEntity> {
        return api.checkIsBlock(imID).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取所有未读公告
     */
    fun getAnnouncement(): Single<ArrayList<NoticeEntity>> {
        return api.getAnnouncement().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 标记公告已阅读
     */
    fun announcementRead(id: Int) {
        api.announcementRead(id)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({}, {})
    }

    /**
     * 获取质检处罚历史
     */
    fun getPenaltyHistory(): Single<PenaltyHistoryEntity> {
        return api.getPenaltyHistory().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取质检处罚通知
     *  @param penaltyId 不为空时，则获取指定id的质检处罚通知
     */
    fun getPenaltyLatestNotify(imID: String, penaltyId: String): Single<IMPenaltiesData> {
        return api.getPenaltyLatestNotify(imID, penaltyId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播流程
     */
    fun newAnchorTask(): Single<NewAnchorEntity> {
        return api.newAnchorTask().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播“恭喜”通知已读
     */
    fun newReadCongrats(): Single<RegActivateEntity> {
        return api.newReadCongrats().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取ai会话的id列表
     */
    fun getAutoChatIds(): Single<ArrayList<AiAutoChatEntity>> {
        return api.getAutoChatIds().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播流程
     */
    fun getSelectModelList(): Single<ArrayList<SelectModelEntity>> {
        return api.getSelectModelList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新账号新手选择排班的列表
     */
    fun getAnchorShiftList(): Single<AnchorShiftEntity> {
        return api.getAnchorShiftList().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新账号新手选择排班的提交
     */
    fun submitAnchorShift(shiftId: String): Single<Any> {
        return api.submitAnchorShift(shiftId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新账号新手任务选择model
     */
    fun selectModel(modelId: String): Single<Any> {
        return api.selectModel(modelId).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 标记主播已经点击邀请好友Tab
     */
    fun invitationRead(): Single<Any> {
        return api.invitationRead().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播绑定邀请码
     */
    fun newBindCode(code: String): Single<Any> {
        return api.newBindCode(code).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    fun skipBindCode(): Single<Any> {
        return api.skipBindCode().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 老主播邀请好友
     */
    fun inviteFriends(): Single<InviteFriendEntity> {
        return api.inviteFriends().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 历史邀请成功记录
     */
    fun invitationHistory(): Single<InviteHistoryListEntity> {
        return api.invitationHistory().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 用户上线时，查询是否是高价值的会话
     */
    fun getOnlineTagUser(imids: String): Single<OnlineTagEntity> {
        return api.getOnlineTagUser(imids).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取主播薪资余额相关数据
     */
    fun getPropertyInfo(): Single<PropertyInfoEntity> {
        return api.getPropertyInfo().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 查询剩余提现次数
     */
    fun queryWithdrawNum(dateType: Int = 0): Single<WithdrawNumEntity> {
        return api.queryWithdrawNum(WithdrawNumReq(dateType)).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 主播提现
     */
    fun chatterWithdraw(amount: Long, password: String): Single<Any> {
        return api.chatterWithdraw(WithdrawReq(amount, password)).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 试岗未通过的主播提现
     */
    fun passChatterWithdraw(amount: Long, password: String, ciphertext: String): Single<Any> {
        return api.passChatterWithdraw(PassWithdrawReq(amount, password, ciphertext)).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 提现单列表（包含详情数据）
     */
    fun withdrawList(startTime: Long, endTime: Long, page: Int = 1): Single<WithdrawListEntity> {
        return api.withdrawList(
            WithdrawProfitReq(
                startTime = startTime,
                endTime = endTime,
                page = page
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 收益流水单列表
     */
    fun profitList(
        type: String = "in",
        startTime: Long,
        endTime: Long,
        page: Int = 1
    ): Single<ProfitListEntity> {
        return api.profitList(
            WithdrawProfitReq(
                type = type,
                startTime = startTime,
                endTime = endTime,
                page = page
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获得首页右下角vip召回入口图标数据
     */
    fun getRevitalizeIn(): Single<RevitalizeInEntity> {
        return api.getRevitalizeIn()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获得首页右下角vip召回列表数据
     */
    fun getRevitalizeData(): Single<RevitalizeEntity> {
        return api.getRevitalize()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 排班入口数据
     */
    fun shiftEntranceData(): Single<ShiftEntranceEntity> {
        return api.shiftEntranceData()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 发送vip老用户召回消息
     */
    fun sentRevitalizeMsg(ids: String, msg: String): Single<Any> {
        return api.sentRevitalizeMsg(ids, msg)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 检查是否需要弹回收预警弹窗
     */
    fun checkShowRepossession(): Single<CheckRepossessionEntity> {
        return api.checkShowRepossession()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 通过自己服务器获取的最近会话列表数据
     */
    fun getInitConversations(): Single<List<ConversationEntity>> {
        return api.getInitConversations()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 删除通过自己服务器获取的最近会话列表中的会话
     */
    fun removeConversation(imId: String) {
        api.removeConversation(imId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe({}, {})
    }

    /**
     * 排班预览页获取tab最近三周时间
     */
    fun getSchedulePreTab(): Single<SchedulePerTabEntity> {
        return api.getSchedulePreTab()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 排班预览页内容
     */
    fun getSchedulePreContent(startDay: String, endDay: String): Single<SchedulePerContentEntity> {
        return api.getSchedulePreContent(startDay, endDay)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     *  获取排班预览页下方文案
     */
    fun getSchedulePreRule(): Single<SchedulePerRulesEntity> {
        return api.getSchedulePreRule()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取请假页下方文案
     */
    fun getLeaveRule(): Single<LeaveRuleEntity> {
        return api.getLeaveRule()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 请假
     */
    fun requestLeave(startDate: String, endDate: String): Single<Any> {
        return api.requestLeave(startDate, endDate)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取请假页下方文案
     */
    fun getShiftChangeRule(): Single<ShiftChangeRuleEntity> {
        return api.getShiftChangeRule()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 查询可换班的班次
     */
    fun getChangeShiftData(changeDay: String, type: Int): Single<ShiftChangeListEntity> {
        return api.getChangeShiftData(changeDay, type)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }


    /**
     * 换班申请
     */
    fun shiftChangeSubmit(
        type: Int,
        startDate: String,
        endDate: String,
        weekDay: String,
        oldShiftId: Int,
        newShiftId: Int
    ): Single<Any> {
        return api.shiftChangeSubmit(startDate, endDate, weekDay, oldShiftId, newShiftId, type)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 请假记录
     */
    fun getLeaveRecords(): Single<LeaveRecordsEntity> {
        return api.getLeaveRecords()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 取消请假
     */
    fun submitLeaveCancel(id: Int): Single<Any> {
        return api.submitLeaveCancel(id)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 中断请假
     */
    fun submitLeaveSuspend(id: Int): Single<Any> {
        return api.submitLeaveSuspend(id)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 中断请假
     */
    fun checkTodayLeave(): Single<TodayLeaveEntity> {
        return api.checkTodayLeave()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 是否能注册
     */
    fun checkAdId(adId: String): Single<RegisterEnableEntity> {
        return api.checkAdId(adId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取引导页配置
     */
    fun checkAppGuidePage(): Single<AppGuideEntity> {
        return api.checkAppGuidePage()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 上传应用列表数据
     */
    fun uploadAppListInfo(cid: String, content: String): Single<Any> {
        return api.uploadAppListInfo(cid, content)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取排行榜数据
     * @param type 1-新人榜，0-W2榜
     */
    fun getRankList(type: Int): Single<DailyRankEntity> {
        return api.getRankList(type)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 首页BonusMatch提示条数据
     */
    fun getBonusTip(): Single<BonusTipEntity> {
        return api.getBonusTip()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 获取惩罚记录
     */
    fun warningCheckInfo(): Single<WarningCheckEntity> {
        return api.warningCheckInfo()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播收口页面数据
     */
    fun getNewbieCenterData(): Single<NewbieCenterEntity> {
        return api.getNewbieCenterData()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 新主播收口页面数据
     */
    fun getLocation(latitude: Double, longitude: Double): Single<CheckPosEntity> {
        return api.getLocation(latitude, longitude)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 校验密钥口令
     */
    fun regCheckCode(tmpCode: String, code: String, nickname: String): Single<CheckCodeEntity> {
        return api.regCheckCode(tmpCode, code, nickname)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                result.checkData()
            }
    }

    /**
     * 完成注册
     */
    fun finishRegSubmit(tmpCode: String, password: String): Single<AuthUserEntity> {
        return api.finishRegSubmit(tmpCode, password)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).map { result ->
                if (result.isSuccessful()) {
                    result.data?.run {
                        if (this.regStatus == 0) {
                            UserController.login(this)
                        }
                        StatusHeartbeatManager.stopHeartbeat()
                        StatusHeartbeatManager.startHeartbeat()
                    }
                    if (!result.data.checkValid()) {
                        result.code = -101
                    }
                }
                result.checkData()
            }
    }
}