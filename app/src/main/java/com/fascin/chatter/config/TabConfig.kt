package com.fascin.chatter.config

import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.AppContext.context
import com.iandroid.allclass.lib_common.beans.HomeTabEntity

object TabConfig {
    const val homeLikeTabWlm = 1001   //like-wlm
    const val homeLikeTabCupidChat = 1002   //like-cupidchat
    const val homeLikeTabILike = 1003   //like-ilike

    const val meMessageSettingGreeting = 1004   // me-message-greeting
    const val meMessageSettingMsg = 1005   //me-message-msg

    const val userMessageSettingGreeting = 1006   // user-message-greeting
    const val userMessageSettingMsg = 1007   //user-message-msg

    const val findChatsTabAgo = 1008   // 7 days ago
    const val findChatsTabAgoTwo = 1009   // 3～7 days ago
    const val findChatsTabWithin = 1010   // Within 7 days

    // 会话列表关系价值tab标签id
    const val conversionAll = 10086
    const val conversionUnlock = 1011
    const val conversionPrime = 1012
    const val conversionRising = 1013
    const val conversionNewChat = 1014
    const val conversionFreshman = 1015
    const val conversionType = 1016
    const val conversionUnread = 1017

    const val balanceIncome = 1018
    const val balanceWithdraw = 1019
    const val balanceDeduct = 1020

    fun getLikeTabList(): List<HomeTabEntity> {
        var tabList = ArrayList<HomeTabEntity>()
        tabList.add(HomeTabEntity().also {
            it.default = 1
            it.title = context.getString(R.string.likes_tab_wlm)
            it.id = homeLikeTabWlm
            it.url = "user/likelist?tag=1"
        })

        tabList.add(HomeTabEntity().also {
            it.id = homeLikeTabCupidChat
            it.title = context.getString(R.string.likes_tab_cupid_chat)
            it.url = "user/likelist?tag=2"
        })

        tabList.add(HomeTabEntity().also {
            it.id = homeLikeTabILike
            it.title = context.getString(R.string.likes_tab_u_like)
            it.url = "user/likelist?tag=3"
        })

        return tabList
    }

    /**
     * message setting tabList
     */
    fun getMessageSettingTabList(): List<HomeTabEntity> {
        val tabList = ArrayList<HomeTabEntity>()
        tabList.add(HomeTabEntity().also {
            it.default = 1
            it.title = context.getString(R.string.message_setting_tab_greeting)
            it.id = meMessageSettingGreeting
            it.url = "tool/greetinglist?type=0"
        })

        tabList.add(HomeTabEntity().also {
            it.id = meMessageSettingMsg
            it.title = context.getString(R.string.message_setting_tab_msg)
            it.url = "tool/greetinglist?type=1"
        })
        return tabList
    }

    /**
     * user message setting tabList
     */
    fun getUserMessageSettingTabList(): List<HomeTabEntity> {
        val tabList = ArrayList<HomeTabEntity>()
        tabList.add(HomeTabEntity().also {
            it.default = 1
            it.title = context.getString(R.string.message_setting_tab_greeting)
            it.id = userMessageSettingGreeting
            it.url = "tool/model/greeting/list?type=0"
        })

        tabList.add(HomeTabEntity().also {
            it.id = userMessageSettingMsg
            it.title = context.getString(R.string.message_setting_tab_msg)
            it.url = "tool/model/greeting/list?type=1"
        })
        return tabList
    }

    /**
     * user find chats tabList
     */
    fun getFindChatsTabList(): List<HomeTabEntity> {
        val tabList = ArrayList<HomeTabEntity>()
        tabList.add(HomeTabEntity().also {
            it.default = 1
            it.title = context.getString(R.string.find_chats_tab_ago)
            it.id = findChatsTabAgo
            it.url = ""
        })

        tabList.add(HomeTabEntity().also {
            it.id = findChatsTabAgoTwo
            it.title = context.getString(R.string.find_chats_tab_ago_3_7)
            it.url = ""
        })

        tabList.add(HomeTabEntity().also {
            it.id = findChatsTabWithin
            it.title = context.getString(R.string.find_chats_tab_within)
            it.url = ""
        })
        return tabList
    }

    /**
     * 会话列表关系价值标签tab
     */
    fun getConversationTabList(): ArrayList<HomeTabEntity> {
        return ArrayList<HomeTabEntity>().apply {
            add(HomeTabEntity().apply {
                id = conversionAll
                title = context.getString(R.string.conversation_tab_all)
            })
            add(HomeTabEntity().apply {
                id = conversionUnlock
                title = context.getString(R.string.conversation_tab_unlock)
            })
            add(HomeTabEntity().apply {
                id = conversionPrime
                title = context.getString(R.string.conversation_tab_prime)
            })
            add(HomeTabEntity().apply {
                id = conversionRising
                title = context.getString(R.string.conversation_tab_rising)
            })
            add(HomeTabEntity().apply {
                id = conversionNewChat
                title = context.getString(R.string.conversation_tab_new_chat)
            })
//            add(HomeTabEntity().apply {
//                id = conversionFreshman
//                title = context.getString(R.string.conversation_tab_freshman)
//            })
            //TODO close type tab by mask
            /*add(HomeTabEntity().apply {
                id = conversionType
                title = context.getString(R.string.conversation_tab_type)
            })*/
        }
    }

}