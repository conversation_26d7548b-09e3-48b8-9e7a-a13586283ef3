package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ProfitListBean
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.activity_reward_detail.llInReason
import kotlinx.android.synthetic.main.activity_reward_detail.llInTaskReward
import kotlinx.android.synthetic.main.activity_reward_detail.tvInChatterId
import kotlinx.android.synthetic.main.activity_reward_detail.tvInPPVCommission
import kotlinx.android.synthetic.main.activity_reward_detail.tvInReason
import kotlinx.android.synthetic.main.activity_reward_detail.tvInReward
import kotlinx.android.synthetic.main.activity_reward_detail.tvInRewardDate
import kotlinx.android.synthetic.main.activity_reward_detail.tvInTaskLevel
import kotlinx.android.synthetic.main.activity_reward_detail.tvInTaskPP
import kotlinx.android.synthetic.main.activity_reward_detail.tvInTaskPV
import kotlinx.android.synthetic.main.activity_reward_detail.tvInTaskTime
import kotlinx.android.synthetic.main.activity_reward_detail.tvInUnlockedPP
import kotlinx.android.synthetic.main.activity_reward_detail.tvInUnlockedPPV
import kotlinx.android.synthetic.main.activity_reward_detail.tvInUnlockedPV

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 奖励明细
 */
class RewardDetailActivity : ChatterBaseActivity() {
    private var profitInfoBean: ProfitListBean? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_reward_detail)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        profitInfoBean = parseJsonParams<ProfitListBean>()

        profitInfoBean?.apply {
            setTitle(title)
            tvInChatterId.setRightText(cid)
            tvInRewardDate.setRightText(createdAt)

            llInTaskReward.show(type == 1)

            details?.let {
                tvInTaskTime.show(it.period.isNotEmpty())
                tvInTaskTime.setRightText(it.period)

                tvInTaskLevel.setRightText(it.taskLvlName)
                tvInTaskPP.setRightText("$moneySymbol ${it.rewardPP}")
                tvInTaskPV.setRightText("$moneySymbol ${it.rewardPV}")

                tvInUnlockedPP.setRightText(it.pp)
                tvInUnlockedPV.setRightText(it.pv)
                tvInUnlockedPPV.setRightText(it.ppvCommission)
                tvInPPVCommission.setRightText("$moneySymbol ${it.ppvMoney}")

                llInReason.show(it.reason.isNotEmpty())
                tvInReason.text = it.reason
            }
            tvInReward.setRightText("$moneySymbol $money")
        }
    }
}