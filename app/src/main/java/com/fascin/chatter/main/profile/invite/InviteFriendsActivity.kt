package com.fascin.chatter.main.profile.invite

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.SpanUtil
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.activity_invite_friends.ivInviteBack
import kotlinx.android.synthetic.main.activity_invite_friends.ivInviteHistory
import kotlinx.android.synthetic.main.activity_invite_friends.rvInviteHistory
import kotlinx.android.synthetic.main.activity_invite_friends.tvCanEarnMoney
import kotlinx.android.synthetic.main.activity_invite_friends.tvCopyCode
import kotlinx.android.synthetic.main.activity_invite_friends.tvCurrentBalance
import kotlinx.android.synthetic.main.activity_invite_friends.tvInviteHistoryEmpty
import kotlinx.android.synthetic.main.activity_invite_friends.tvInviteTime
import kotlinx.android.synthetic.main.activity_invite_friends.tvSuccessInvited

/**
 *  @author: LXL
 *  @description: 邀请好友
 *  @date: 2024/9/2 16:18
 */
class InviteFriendsActivity : ChatterBaseActivity() {
    private var inviteSuccessAdapter: InviteSuccessAdapter? = null

    private val viewModel by lazy {
        ViewModelProvider(this, InviteFriendsViewModel.ViewModeFactory())[InviteFriendsViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_invite_friends)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        showTitleBar(false)
        immersiveStatusBarWithMargin(ivInviteBack)

        inviteSuccessAdapter = InviteSuccessAdapter()
        rvInviteHistory.adapter = inviteSuccessAdapter
        rvInviteHistory.isNestedScrollingEnabled = false
        rvInviteHistory.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        viewModel.inviteFriends()
        viewModel.inviteFriendResult.observe(this) { entity ->
            tvCanEarnMoney.text = entity.money
            tvInviteTime.text = "${entity.st} ~ ${entity.et}"
            tvSuccessInvited.text = entity.successNum
            tvCurrentBalance.text = entity.successMoney
            SpanUtil.create().addSection(getString(R.string.invite_copy_code))
                .addForeColorSection(" ${entity.code}", getCompatColor(R.color.white))
                .showIn(tvCopyCode)

            tvCopyCode.clickWithTrigger {
                copyToClipboard(entity.code)
            }
            inviteSuccessAdapter?.updateData(entity.history)
            tvInviteHistoryEmpty.show(entity.history.isNullOrEmpty())
        }

        ivInviteHistory.clickWithTrigger {
            startActivity(Intent(this, InviteHistoryActivity::class.java))
        }

        ivInviteBack.clickWithTrigger {
            finish()
        }
    }

    private fun copyToClipboard(text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Copied Text", text)
        clipboard.setPrimaryClip(clip)
        ToastUtils.showToast("Copied successfully")
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }
}