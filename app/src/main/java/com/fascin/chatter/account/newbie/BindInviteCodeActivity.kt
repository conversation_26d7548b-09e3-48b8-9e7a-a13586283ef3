package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.databinding.ActivityBindInviteCodeBinding
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger

class BindInviteCodeActivity : BaseTaskActivity() {

    private lateinit var binding: ActivityBindInviteCodeBinding
    private val viewModel: ProfileViewModel by viewModels()

    override fun determineCurrentTaskType(): TaskType? = TaskType.BIND_CODE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBindInviteCodeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
    }

    private fun initView() {
        binding.etBindCode.addTextChangedListener {
            val characterCount = it?.length ?: 0
            binding.tvError.isEnabled = characterCount >= 8
        }

        binding.tvBind.clickWithTrigger {
            viewModel.newBindCode(binding.etBindCode.text.toString()) {
                SimpleRxBus.post(EventProfileUpdate())
                SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                onTaskCompleted()
            }
        }

        binding.tvNext.clickWithTrigger {
            viewModel.skipBindCode {
                SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                onTaskCompleted()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}