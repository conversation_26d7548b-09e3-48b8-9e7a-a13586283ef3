package com.fascin.chatter.account.newbie

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.AnchorShiftItemEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.itemview_new_anchor_shift.view.ivSelect
import kotlinx.android.synthetic.main.itemview_new_anchor_shift.view.tvDisplayTime
import kotlinx.android.synthetic.main.itemview_new_anchor_shift.view.tvLocalTime

/**
 * @Desc: 新主播流程班次选择列表
 * @Created: Quan
 * @Date: 2024/11/26
 */
class AnchorShiftAdapter : RecyclerView.Adapter<AnchorShiftAdapter.ViewHolder>() {

    var selectedShiftId = -1 // 选中的item
    private var selectHolder: ViewHolder? = null
    private val dataList = mutableListOf<AnchorShiftItemEntity>()
    private var onItemClickListener: ((AnchorShiftItemEntity) -> Unit)? = null

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun setOnItemClickListener(listener: (AnchorShiftItemEntity) -> Unit) {
        this.onItemClickListener = listener
    }

    fun updateData(data: List<AnchorShiftItemEntity>) {
        selectedShiftId = -1
        selectHolder = null
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_new_anchor_shift, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView?.also { itemView ->
            itemView.tvLocalTime.text = item.shiftDate
            itemView.tvDisplayTime.text = item.zoneShiftDate
            if (selectedShiftId == item.shiftId) {
                selectHolder = holder
                itemView.ivSelect.isSelected = true
            } else {
                itemView.ivSelect.isSelected = false
            }
        }
        holder.itemView.clickWithTrigger {
            if (selectedShiftId != item.shiftId) {
                selectedShiftId = item.shiftId
                selectHolder?.also { select ->
                    select.itemView.ivSelect.isSelected = false
                }
                holder.itemView.ivSelect.isSelected = true
                selectHolder = holder
                onItemClickListener?.invoke(item)
            }
        }
    }

}