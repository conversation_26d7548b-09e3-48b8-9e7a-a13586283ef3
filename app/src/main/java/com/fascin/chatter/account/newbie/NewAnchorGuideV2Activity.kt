package com.fascin.chatter.account.newbie

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.account.newbie.navigation.TaskNavigationGraph
import com.fascin.chatter.account.newbie.navigation.TaskNavigationManager
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.bean.CustomerServiceIntent
import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.bean.TaskData
import com.fascin.chatter.config.Config
import com.fascin.chatter.databinding.ActivityNewAnchorGuideV2Binding
import com.fascin.chatter.databinding.ItemNewAnchorGuideTaskBinding
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.toPx
import java.util.Locale
import java.util.concurrent.TimeUnit

class NewAnchorGuideV2Activity : FasBaseActivity(), View.OnClickListener {

    private lateinit var binding: ActivityNewAnchorGuideV2Binding

    private val taskAdapter by lazy { TaskAdapter() }

    private val viewModel: AccountViewModel by viewModels()
    private var countDownTimer: CountDownTimer? = null
    private var isCountDowning = true  //是否在倒计时

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNewAnchorGuideV2Binding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initData()
    }

    private fun initView() {
        binding.tvCustomService.paint.isUnderlineText = true
        binding.rvTask.apply {
            layoutManager = LinearLayoutManager(this@NewAnchorGuideV2Activity)
            adapter = taskAdapter
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val position = parent.getChildAdapterPosition(view)

                    if (position != 0) {
                        outRect.top = 20.toPx
                    }
                }
            })
        }

        binding.ivCustomService.setOnClickListener(this)
        binding.tvCustomService.setOnClickListener(this)
        binding.tvActive.setOnClickListener(this)
    }

    private fun initData() {
        viewModel.newAnchorResult.observe(this) {
            updateUI(it)

            // 创建并设置导航图
            createNavigationGraph(it)
        }
        viewModel.newAnchorError.observe(this) {
            viewModel.newAnchorGuide()
        }

        //刷新接口更新任务状态
        viewModel.compositeDisposable?.add(
            SimpleRxBus.observe(UINewAnchorTaskStatusEvent::class) {
                viewModel.newAnchorGuide()
            }
        )

        AppRepository.statusChange(Config.statusNoActive)
        viewModel.newAnchorGuide()
    }

    private fun updateUI(entity: NewAnchorEntity) {
        val tasks = listOfNotNull(
            entity.bindCodeTask,
            entity.courseTask,
            entity.bankAccountTask,
            entity.selectModelTask,
            entity.selectShiftTask
        )

        val taskFinishCount = tasks.count { it.status == 1 }

        if (isCountDowning) {
            val totalTime = entity.expireDuration * 1000L //倒计时
            countDownTimer = object : CountDownTimer(totalTime, 1000) { // 每秒更新一次
                override fun onTick(millisUntilFinished: Long) {
                    val days = TimeUnit.MILLISECONDS.toDays(millisUntilFinished)
                    val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished) % 24
                    val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) % 60
                    val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) % 60

                    val timeLeftFormatted = String.format(
                        Locale.getDefault(), "%dd:%dh:%02dm:%02ds", days, hours, minutes, seconds)
                    binding.tvRemaining.text = timeLeftFormatted
                }

                override fun onFinish() {
                    binding.tvRemaining.text = getString(R.string.new_anchor_remaining_empty)
                    if (taskFinishCount < 3) {
                        UserController.tickOffline()
                    }
                }
            }.start()
            isCountDowning = false
        }

        taskAdapter.submitList(tasks)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun onClick(view: View) {
        when(view) {
            binding.ivCustomService, binding.tvCustomService -> {
                //跳转客服
                routeAction(ActionType.actionOnlineSupport) {
                    it.param = CustomerServiceIntent().also { intent ->
                        intent.title = getString(R.string.customer_service_qa)
                        intent.accountId = AppController.getQAAccount()
                    }
                }
            }

            binding.tvActive -> {
                TaskNavigationManager.executeTask(this@NewAnchorGuideV2Activity)
                finish()
            }
        }
    }

    private fun createNavigationGraph(entity: NewAnchorEntity) {
        // 创建导航图并共享给子Activity
        val navigationGraph = TaskNavigationGraph.create(entity)
        TaskNavigationManager.setEntity(entity)
        TaskNavigationManager.setNavigationGraph(navigationGraph)
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
    }
}

private class TaskAdapter : RecyclerView.Adapter<TaskViewHolder>() {
    private val data: MutableList<TaskData> = mutableListOf()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): TaskViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_new_anchor_guide_task, parent, false)
        return TaskViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: TaskViewHolder,
        position: Int
    ) {
        val item = data.getOrNull(position) ?: return
        holder.binding.apply {
            ivTaskStatus.isSelected = item.status == 1
            tvTaskName.text = item.title
        }
    }

    override fun getItemCount(): Int = data.size

    @SuppressLint("NotifyDataSetChanged")
    fun submitList(list: List<TaskData>) {
        data.clear()
        data.addAll(list)
        notifyDataSetChanged()
    }


}

private class TaskViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    val binding = ItemNewAnchorGuideTaskBinding.bind(itemView)
}