package com.fascin.chatter.account.newbie

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.component.views.SUButtonStatus
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_new_anchor_shift.anchorBack
import kotlinx.android.synthetic.main.activity_new_anchor_shift.btnSubmit
import kotlinx.android.synthetic.main.activity_new_anchor_shift.rvSelectShift
import kotlinx.android.synthetic.main.activity_new_anchor_shift.tv1

/**
 * @Desc: 新主播流程班次选择列表
 * @Created: Quan
 * @Date: 2024/11/26
 */
class NewAnchorShiftActivity : ChatterBaseActivity() {

    private var viewModel: AccountViewModel? = null
    private var adapter: AnchorShiftAdapter? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_new_anchor_shift)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setViewUI()
        setModel()
        setListener()
    }

    private fun setViewUI() {
        btnSubmit.setText("Confirm")
        btnSubmit.setViewHeight(56)
        btnSubmit.setButtonStatus(
            SUButtonStatus.Disabled,
            disabledBg = R.drawable.ic_anchor_btn_dis,
            disabledColor = "#ffffff"
        )
        rvSelectShift.layoutManager = LinearLayoutManager(this)
        adapter = AnchorShiftAdapter()
        rvSelectShift.adapter = adapter
    }

    private fun setModel() {
        viewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        viewModel?.anchorShitResult?.observe(this) {
            tv1.text = buildString {
                append("Your Local Time")
                if (it.location.isNotEmpty()) {
                    append("\n")
                    append(it.location)
                }
            }
            adapter?.updateData(it.shiftList)
        }
        viewModel?.anchorShitSubmitResult?.observe(this) {
            if (it) {
                ToastUtils.showToast(getString(R.string.balance_success))
                SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                finish()
            } else {
                btnSubmit.setButtonStatus(
                    SUButtonStatus.Activated,
                    activatedBg = R.drawable.ic_anchor_btn_act
                )
            }
        }
        viewModel?.getAnchorShiftList()
    }

    private fun setListener() {
        anchorBack.clickWithTrigger {
            finish()
        }
        adapter?.setOnItemClickListener {
            if (btnSubmit.isLoadingStatus()) return@setOnItemClickListener
            btnSubmit.setButtonStatus(
                SUButtonStatus.Activated,
                activatedBg = R.drawable.ic_anchor_btn_act
            )
        }
        btnSubmit.clickWithTrigger {
            if (adapter != null && adapter?.selectedShiftId!! > 0) {
                btnSubmit.setButtonStatus(
                    SUButtonStatus.Loading,
                    loadingBg = R.drawable.ic_anchor_btn_act
                )
                viewModel?.submitAnchorShift(adapter?.selectedShiftId.toString())
            }
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}