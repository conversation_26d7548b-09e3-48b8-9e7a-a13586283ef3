package com.fascin.chatter.account.newbie

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.bean.BindModelIntent
import com.fascin.chatter.bean.SelectModelEntity
import com.fascin.chatter.bean.event.UISelectModelEvent
import com.fascin.chatter.bean.event.UISelectModelFailedEvent
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_select_model.rvSelectModel
import kotlinx.android.synthetic.main.activity_select_model.titleBack
import kotlinx.android.synthetic.main.activity_select_model.tvSelectNum

/**
 * @Desc: 新手引导任务：选择model
 * @Created: Quan
 * @Date: 2024/8/15
 */
class SelectModelActivity : FasBaseActivity() {

    private var intentData: BindModelIntent? = null
    private var accountViewModel: AccountViewModel? = null
    private var adapter: SelectModelAdapter? = null
    private var selectedCount: Int = 0
    private var maxCount: Int = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_select_model)
        initStatusBarMode()
        initViewData()
        setListener()
    }

    private fun initViewData() {
        intentData = parseJsonParams<BindModelIntent>()
        intentData?.let {
            if (it.from == 1) maxCount = it.maxNum
        }
        adapter = SelectModelAdapter()
        rvSelectModel.layoutManager = GridLayoutManager(this, 2)
        rvSelectModel.adapter = adapter
        accountViewModel = ViewModelProvider(
            this, AccountViewModel.ViewModeFactory()
        )[AccountViewModel::class.java]
        accountViewModel?.getSelectModelList()
    }

    private fun setListener() {

        titleBack.clickWithTrigger {
            finish()
        }

        accountViewModel?.selectModelResult?.observe(this) {
            if (it.isNullOrEmpty()) {
                ToastUtils.showToast("Request data is empty!")
            }
            setPageData(it)
        }

        accountViewModel?.selectModelError?.observe(this) {
            ToastUtils.showToast(it)
        }

        accountViewModel?.compositeDisposable?.add(SimpleRxBus.observe(UISelectModelFailedEvent::class) {
            // 此model不可绑定，绑定失败,刷新数据
            accountViewModel?.getSelectModelList()
        })

        accountViewModel?.compositeDisposable?.add(SimpleRxBus.observe(UISelectModelEvent::class) {
            selectedCount++
            adapter?.setSelectedCount(selectedCount)
            adapter?.modelSelected(it.modelId)
            tvSelectNum.text = String.format(
                getString(R.string.select_model_num),
                maxCount,
                selectedCount,
                maxCount
            )
            if (selectedCount >= maxCount) finish()
        })
    }

    private fun setPageData(data: ArrayList<SelectModelEntity>) {
        selectedCount = 0
        data.filter { it.isBind() }.let {
            selectedCount = if (it.isNullOrEmpty()) 0 else it.size
            tvSelectNum.text = String.format(
                getString(R.string.select_model_num),
                maxCount,
                selectedCount,
                maxCount
            )
        }
        adapter?.setData(data)
    }


    override fun onDestroy() {
        super.onDestroy()
        SimpleRxBus.post(UINewAnchorTaskStatusEvent())
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }
}