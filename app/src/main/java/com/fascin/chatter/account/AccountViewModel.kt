package com.fascin.chatter.account

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.AnchorShiftEntity
import com.fascin.chatter.bean.CheckCodeEntity
import com.fascin.chatter.bean.CheckPosEntity
import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.bean.NewbieCenterEntity
import com.fascin.chatter.bean.RegActivateEntity
import com.fascin.chatter.bean.RegisterEnableEntity
import com.fascin.chatter.bean.SelectModelEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.AuthUserEntity
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
created by wangkm
on 2020/9/12.
 */
class AccountViewModel : BaseViewModel() {
    var loginResult = MutableLiveData<AuthUserEntity>()
    var loginError = MutableLiveData<String>()
    var newAnchorResult = MutableLiveData<NewAnchorEntity>()
    var newAnchorError = MutableLiveData<String>()
    var selectModelResult = MutableLiveData<ArrayList<SelectModelEntity>>()
    var selectModelError = MutableLiveData<String>()
    var anchorShitResult = MutableLiveData<AnchorShiftEntity>()
    var anchorShitSubmitResult = MutableLiveData<Boolean>()

    var checkCodeResult = MutableLiveData<CheckCodeEntity>()
    var checkCodeError = MutableLiveData<String>()

    var checkAdIdResult = MutableLiveData<RegisterEnableEntity>()

    var newbieCenterResult = MutableLiveData<NewbieCenterEntity>()
    var newbieCenterError = MutableLiveData<String>()

    var locationResult = MutableLiveData<CheckPosEntity>()
    var locationError = MutableLiveData<String>()

    var checkRegResult = MutableLiveData<AuthUserEntity>()
    var checkRegError = MutableLiveData<String>()

    //登录
    fun accountLogin(account: String, password: String) {
        compositeDisposable?.add(
            AppRepository.accountLogin(account, password)
                .subscribe({
                    loginResult.postValue(it)
                }, {
                    var errMsg = ErrorCodeCheckUtils.getError(it)
                    loginError.postValue(errMsg)
                    ToastUtils.showToast(errMsg)
                })
        )
    }

    /**
     * 新主播引导
     */
    fun newAnchorGuide() {
        compositeDisposable?.add(
            AppRepository.newAnchorTask()
                .subscribe({
                    newAnchorResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                    newAnchorError.postValue(errMsg)
                })
        )
    }

    /**
     * 新主播"恭喜"通知已读
     */
    fun newReadCongrats(callback: (RegActivateEntity) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.newReadCongrats()
                .subscribe({
                    callback.invoke(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 新账号新手任务选择model的列表
     */
    fun getSelectModelList() {
        compositeDisposable?.add(
            AppRepository.getSelectModelList()
                .subscribe({
                    selectModelResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    selectModelError.postValue(errMsg)
                    ToastUtils.showToast(errMsg)
                })
        )
    }

    /**
     * 是否能注册
     */
    fun checkAdId(adId: String) {
        Log.e("checkAdId", adId)
        compositeDisposable?.add(
            AppRepository.checkAdId(adId)
                .subscribe({
                    checkAdIdResult.postValue(it)
                }, {
                })
        )
    }

    /**
     * 新账号新手选择排班的列表
     */
    fun getAnchorShiftList() {
        compositeDisposable?.add(
            AppRepository.getAnchorShiftList()
                .subscribe({
                    anchorShitResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                })
        )
    }

    /**
     * 新账号新手选择排班的提交
     */
    fun submitAnchorShift(shiftId: String) {
        compositeDisposable?.add(
            AppRepository.submitAnchorShift(shiftId)
                .subscribe({
                    anchorShitSubmitResult.postValue(true)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                    anchorShitSubmitResult.postValue(false)
                })
        )
    }

    /**
     * 新主播收口页面数据
     */
    fun getNewbieCenterData() {
        compositeDisposable?.add(
            AppRepository.getNewbieCenterData()
                .subscribe({
                    newbieCenterResult.postValue(it)
                }, {
                    ToastUtils.showToast(it.message)
                    newbieCenterError.postValue("")
                })
        )
    }

    /**
     * 获取定位
     */
    fun getLocation(latitude: Double = 0.0, longitude: Double = 0.0) {
        compositeDisposable?.add(
            AppRepository.getLocation(latitude, longitude)
                .subscribe({
                    locationResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                    locationError.postValue(it.message)
                })
        )
    }

    /**
     * 自主创号 检查口令
     */
    fun regCheckCode(tmpCode: String, code: String, nickname: String) {
        compositeDisposable?.add(
            AppRepository.regCheckCode(tmpCode, code, nickname)
                .subscribe({
                    checkCodeResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                    checkCodeError.postValue(errMsg)
                })
        )
    }

    /**
     * 完成注册
     */
    fun finishRegSubmit(tmpCode: String, password: String) {
        compositeDisposable?.add(
            AppRepository.finishRegSubmit(tmpCode, password)
                .subscribe({
                    checkRegResult.postValue(it)
                }, {
                    val errMsg = ErrorCodeCheckUtils.getError(it)
                    ToastUtils.showToast(errMsg)
                    checkRegError.postValue(errMsg)
                })
        )
    }


    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return AccountViewModel() as T
        }
    }
}