package com.fascin.chatter.account.newbie

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskNavigationManager
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.databinding.ActivityCourseActiveBinding
import com.fascin.chatter.main.course.CourseViewModel
import com.iandroid.allclass.lib_common.utils.ToastUtils
import okhttp3.internal.concurrent.Task

class CourseActiveActivity : BaseTaskActivity() {

    private lateinit var binding: ActivityCourseActiveBinding
    private val viewModel: CourseViewModel by viewModels()

    override fun determineCurrentTaskType(): TaskType? = TaskType.COURSE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCourseActiveBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initData()
    }

    private fun initView() {

    }

    private fun initData() {
        val id = TaskNavigationManager.getEntity()?.courseTask?.courseId ?: 0 // 课程id

        viewModel.coursesResult.observe(this) { courses ->
            val first = courses.firstOrNull()
            if (first == null) {
                ToastUtils.showToast("Course content url error")
                return@observe
            }

            TaskNavigationManager.getEntity()?.courseTask?.courseName = first.title
        }

        viewModel.getCourses(
            lastId = 0, //默认为0
            categoryId = id
        )
    }
}