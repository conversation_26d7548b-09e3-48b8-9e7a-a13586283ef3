<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Android 13 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 拍照 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 读取设备IMEI号 -->
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" /> <!-- 定位的权限 -->
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />

    <application
        android:name=".ToolApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/app_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:launchMode="singleTask"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/app_logo"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,android:icon,android:label">

        <!--配置debug与release上传crash开关-->
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="${firebaseCrashlyticsCollectionEnabled}" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/map_api_key" />

        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Startup">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="{applicationId}.launcher" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="{applicationId}.launcher.fcm" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}"
                    android:path="/thirdpush"
                    android:scheme="agoo" />
            </intent-filter>

        </activity>
        <activity
            android:name=".main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- app scheme -->
                <data
                    android:host="${applicationId}"
                    android:scheme="chatter" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- app scheme -->
                <data
                    android:host="${applicationId}"
                    android:scheme="http" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- app scheme -->
                <data
                    android:host="${applicationId}"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <activity
            android:name=".account.LoginActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".account.newbie.SelectModelActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".pay.PrivacyListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".pay.MediaPreviewActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".main.profile.edit.NotificationActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".main.profile.OtherProfileActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".main.profile.MessageSettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".main.task.TaskHistoricalDetailActivity"
            android:screenOrientation="portrait" />


        <activity
            android:name=".pay.PrivacyImgActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".im.ChatActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <activity
            android:name=".im.ImageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.course.CourseListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.course.CourseSortActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.course.QuestionnaireActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.fascin.chatter.pay.PrivacyMultipleSendActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".im.preview.MultiplePreviewActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.chats.FindChatsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.fascin.chatter.main.task.TaskHistoricalListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.TrafficActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.MPCActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.match.BonusMatchHubActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.PenaltiesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.CustomerServiceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.OnlineSupportActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.chats.VipContactSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".account.newbie.NewAnchorGuideActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".account.newbie.NewAnchorGuideV2Activity"
            android:screenOrientation="portrait"/>
        <activity android:name=".account.newbie.BindInviteCodeActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".account.newbie.CourseActiveActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".main.profile.invite.InviteFriendsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.invite.InviteHistoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.withdraw.WithdrawActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.withdraw.BalanceDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.withdraw.RewardDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.withdraw.WithdrawDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.withdraw.DeductDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.schedule.ScheduleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.schedule.ScheduleRequestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.schedule.RequestLeaveActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.schedule.ShiftChangeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.schedule.LeaveRecordsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".account.newbie.NewAnchorShiftActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".main.task.DailyRankActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.profile.guide.FeatureGuideActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".account.newbie.NewbieCenterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".account.create.FillAccountInfoActivity"
            android:screenOrientation="portrait" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="{applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/image_path" />
        </provider>
    </application>

</manifest>